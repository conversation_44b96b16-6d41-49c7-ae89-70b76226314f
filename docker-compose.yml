version: '3.8'

services:
    web:
        image: ghcr.io/linanok/linanok/octane:latest
        ports:
            - "8000:8000"
        volumes:
            - frankenphp-data:/tmp
            - storage-data:/app/storage
        env_file:
            - .env
        environment:
            - APP_NAME=Linanok
            - VITE_APP_NAME=Linanok
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        restart: unless-stopped
        networks:
            - app
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:8000" ]
            interval: 5s
            timeout: 5s
            retries: 5

    queue-worker:
        image: ghcr.io/linanok/linanok/cli:latest
        env_file:
            - .env
        environment:
            - APP_NAME=Linanok
            - VITE_APP_NAME=Linanok
        command: [ "php", "artisan", "horizon" ]
        healthcheck:
            test: [ "CMD-SHELL", "php artisan horizon:status | grep -q 'running'" ]
            interval: 5s
            timeout: 5s
            retries: 5
        volumes:
            - storage-data:/app/storage
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        restart: unless-stopped
        networks:
            - app

    init:
        image: ghcr.io/linanok/linanok/cli:latest
        env_file:
            - .env
        environment:
            - APP_NAME=Linanok
            - VITE_APP_NAME=Linanok
        entrypoint: [ "deployment/init.sh" ]
        volumes:
            - storage-data:/app/storage
        depends_on:
            postgres:
                condition: service_healthy
            redis:
                condition: service_healthy
        networks:
            - app

    postgres:
        image: postgres:17-alpine
        volumes:
            - postgres-data:/var/lib/postgresql/data
        env_file:
            - .env
        environment:
            - POSTGRES_DB=${DB_DATABASE}
            - POSTGRES_USER=${DB_USERNAME}
            - POSTGRES_PASSWORD=${DB_PASSWORD}
        healthcheck:
            test: [ "CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE}" ]
            interval: 5s
            timeout: 5s
            retries: 5
        networks:
            - app
        restart: unless-stopped

    redis:
        image: redis:8-alpine
        volumes:
            - redis-data:/data
        env_file:
            - .env
        command: redis-server --requirepass ${REDIS_PASSWORD}
        healthcheck:
            test: [ "CMD-SHELL", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping" ]
            interval: 5s
            timeout: 5s
            retries: 5
        networks:
            - app
        restart: unless-stopped

volumes:
    postgres-data:
    redis-data:
    frankenphp-data:
    storage-data:

networks:
    app:
        driver: bridge
        ipam:
            config:
                -   subnet: ${TRUSTED_PROXIES}
